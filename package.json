{"name": "beasy-mini-app-react", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "node server", "build": "npm run build:client", "build:client": "vite build --outDir dist/client", "build:server": "vite build --ssr src/entry-server.tsx --outDir dist/server", "preview": "cross-env NODE_ENV=production node server"}, "dependencies": {"compression": "^1.8.1", "react": "^19.1.1", "react-dom": "^19.1.1", "sirv": "^3.0.1"}, "devDependencies": {"@types/express": "^5.0.3", "@types/node": "^24.1.0", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react-swc": "^3.10.2", "cross-env": "^10.0.0", "typescript": "~5.9.2", "vite": "^7.0.6"}}